import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import {
  Zap,
  Sparkles,
  Upload,
  Wand2,
  Image as ImageIcon,
  Type,
  Palette,
  Target,
  Rocket,
  Brain,
  Star,
  Download,
  X,
  ZoomIn,
  Copy
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

function FreeGenerationContent() {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [generatedResult, setGeneratedResult] = useState<any>(null);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const [, navigate] = useLocation();

  const handleImageUpload = (files: FileList | null) => {
    if (files) {
      const newImages = Array.from(files).slice(0, 3); // Máximo 3 imágenes
      setUploadedImages(prev => [...prev, ...newImages].slice(0, 3));
    }
  };

  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleImageClick = (imageUrl: string) => {
    console.log('🖼️ Image clicked, opening modal for:', imageUrl);
    console.log('🔍 Modal state before:', { isModalOpen, selectedImage });

    setSelectedImage(imageUrl);
    setIsModalOpen(true);

    console.log('✅ Modal should now be open with image:', imageUrl);
  };

  const copyImageUrl = async (imageUrl: string) => {
    try {
      await navigator.clipboard.writeText(imageUrl);
      toast({
        title: "✅ URL copiada",
        description: "La URL de la imagen se ha copiado al portapapeles"
      });
    } catch (error) {
      console.error('Error copying URL:', error);
      toast({
        title: "❌ Error al copiar",
        description: "No se pudo copiar la URL automáticamente",
        variant: "destructive"
      });
    }
  };

  const downloadImage = async (imageUrl: string, filename: string = 'emma-ad') => {
    console.log('📥 Starting download for:', imageUrl);

    try {
      // Método 1: Intentar descarga directa con fetch para evitar CORS
      const response = await fetch(imageUrl, {
        mode: 'cors',
        credentials: 'omit'
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `${filename}-${Date.now()}.png`;

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Limpiar el objeto URL
        window.URL.revokeObjectURL(url);

        console.log('✅ Download completed via fetch');
        toast({
          title: "✅ Descarga completada",
          description: "Tu imagen Emma se ha descargado exitosamente"
        });
        return;
      }
    } catch (fetchError) {
      console.log('⚠️ Fetch method failed, trying alternative:', fetchError);
    }

    // Método 2: Descarga directa (funciona en algunos casos)
    try {
      const link = document.createElement('a');
      link.href = imageUrl;
      link.download = `${filename}-${Date.now()}.png`;
      link.target = '_blank';
      link.rel = 'noopener noreferrer';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('✅ Download initiated via direct link');
      toast({
        title: "✅ Descarga iniciada",
        description: "Si no se descarga automáticamente, haz clic derecho en la imagen y selecciona 'Guardar imagen como'"
      });
      return;
    } catch (directError) {
      console.log('⚠️ Direct download failed:', directError);
    }

    // Método 3: Fallback - abrir en nueva pestaña
    try {
      window.open(imageUrl, '_blank');
      toast({
        title: "📱 Imagen abierta en nueva pestaña",
        description: "Haz clic derecho en la imagen y selecciona 'Guardar imagen como' para descargar",
        variant: "default"
      });
    } catch (fallbackError) {
      console.error('❌ All download methods failed:', fallbackError);
      toast({
        title: "❌ Error al descargar",
        description: "Copia la URL de la imagen para descargarla manualmente",
        variant: "destructive"
      });
    }
  };

  const handleFreeGeneration = async () => {
    if (!prompt.trim() && uploadedImages.length === 0) {
      toast({
        title: "¡Necesito algo!",
        description: "Escribe qué quieres o sube una imagen de tu producto",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);

    try {
      // Llamar al endpoint de generación libre con Ideogram (3 versiones por defecto)
      const formData = new FormData();
      formData.append('prompt', prompt);
      formData.append('platform', 'instagram'); // Default platform
      formData.append('size', '1024x1024'); // Default size
      formData.append('num_images', '3'); // Generar 3 versiones por defecto

      uploadedImages.forEach((image, index) => {
        formData.append(`image_${index}`, image);
      });

      console.log('🚀 Making request to free-generation endpoint...');
      const response = await fetch('/api/v1/ad-creator-agent/free-generation', {
        method: 'POST',
        body: formData
      });

      console.log('📡 Response received:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        ok: response.ok
      });

      if (response.ok) {
        console.log('✅ Response is OK, parsing JSON...');
        const responseText = await response.text();
        console.log('📄 Raw response text length:', responseText.length);
        console.log('📄 Raw response text (first 500 chars):', responseText.substring(0, 500));

        let result;
        try {
          result = JSON.parse(responseText);
          console.log('✅ JSON parsed successfully:', result);
        } catch (parseError) {
          console.error('❌ JSON parse error:', parseError);
          console.error('📄 Full response text:', responseText);
          throw new Error(`Failed to parse response as JSON: ${parseError.message}`);
        }

        toast({
          title: "🎉 ¡Anuncio Generado con Ideogram!",
          description: "Emma creó tu anuncio automáticamente"
        });

        // Mostrar el resultado directamente en la página
        setGeneratedResult(result);
      } else {
        console.log('❌ Response not OK, handling error...');
        // Better error handling for malformed JSON responses
        let errorMessage = 'Error en la generación';
        try {
          const errorText = await response.text();
          console.error('📄 Error response text:', errorText);

          try {
            const errorData = JSON.parse(errorText);
            errorMessage = errorData.detail || errorData.message || 'Error en la generación';
          } catch (jsonError) {
            console.error('❌ Failed to parse error response as JSON:', jsonError);
            errorMessage = `Error ${response.status}: ${response.statusText || 'Error del servidor'}`;
            if (errorText && errorText.length < 200) {
              errorMessage += ` - ${errorText}`;
            }
          }
        } catch (textError) {
          console.error('❌ Failed to get error response as text:', textError);
          errorMessage = `Error ${response.status}: ${response.statusText || 'Error del servidor'}`;
        }
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('❌ Caught error in handleFreeGeneration:', error);
      console.error('❌ Error type:', typeof error);
      console.error('❌ Error constructor:', error.constructor.name);
      console.error('❌ Error message:', error.message);
      console.error('❌ Error stack:', error.stack);

      toast({
        title: "Error",
        description: error.message || "Hubo un problema. Inténtalo de nuevo.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50">
      {/* Hero Section profesional estilo Emma */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="relative overflow-hidden rounded-2xl backdrop-blur-xl mb-8 mx-6 mt-6"
      >
        {/* Gradient background Emma */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#3018ef] via-[#4f46e5] to-[#dd3a5a] opacity-95"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

        {/* Elementos flotantes decorativos */}
        <motion.div
          className="absolute right-8 top-8 w-20 h-20 bg-white/20 backdrop-blur-md rounded-2xl transform rotate-12 flex items-center justify-center shadow-2xl border border-white/30"
          animate={{ rotate: [12, 18, 12] }}
          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
        >
          <Zap className="w-10 h-10 text-white" />
        </motion.div>

        <motion.div
          className="absolute left-8 bottom-8 w-16 h-16 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center shadow-xl border border-white/30"
          animate={{ y: [0, -10, 0] }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
        >
          <Sparkles className="w-8 h-8 text-white animate-pulse" />
        </motion.div>

        <div className="relative px-8 py-12 md:py-16 md:px-12">
          <div className="max-w-4xl mx-auto text-center">
            {/* Badge superior */}
            <motion.span
              className="inline-flex items-center bg-white/20 backdrop-blur-md text-white font-semibold px-6 py-3 rounded-full mb-6 border border-white/30 text-sm"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              whileHover={{ scale: 1.05 }}
            >
              <Rocket className="inline-block w-4 h-4 mr-2" />
              Tecnología Emma Pro • Nivel Agencias
            </motion.span>

            {/* Título principal */}
            <motion.h1
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
              className="text-4xl lg:text-6xl font-black mb-6 leading-tight"
            >
              <span className="text-white">Generador </span>
              <span className="bg-gradient-to-r from-[#dd3a5a] via-[#f472b6] to-[#fbbf24] bg-clip-text text-transparent">
                Premium
              </span>
            </motion.h1>

            {/* Subtítulo */}
            <motion.p
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5 }}
              className="text-xl text-white/90 max-w-3xl mx-auto mb-8 leading-relaxed"
            >
              La misma tecnología que usan las agencias top del mundo, ahora en tus manos.
              <br />
              <span className="text-white/70 text-lg">Resultados profesionales en segundos con la IA más avanzada del mercado</span>
            </motion.p>

            {/* Stats profesionales */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto"
            >
              {[
                { value: "Enterprise", label: "Tecnología", icon: Rocket, color: "text-[#dd3a5a]" },
                { value: "Emma AI", label: "Powered", icon: Brain, color: "text-[#fbbf24]" },
                { value: "Agency", label: "Level", icon: Star, color: "text-[#10b981]" },
                { value: "Premium", label: "Results", icon: Zap, color: "text-[#06b6d4]" }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.05, y: -2 }}
                  transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  className="bg-white/20 backdrop-blur-md rounded-xl p-4 border border-white/30 shadow-xl"
                >
                  <stat.icon className={`w-6 h-6 ${stat.color} mx-auto mb-2`} />
                  <div className="text-white font-bold text-lg">{stat.value}</div>
                  <div className="text-white/70 text-sm">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-8"
        >
          {/* Input Section con glassmorphism */}
          <Card className="bg-white/70 backdrop-blur-sm border border-gray-200/50 shadow-xl">
            <CardContent className="p-8">
              <div className="space-y-6">
                {/* Text Input */}
                <div>
                  <label className="text-gray-800 font-semibold mb-3 block text-lg">
                    ¿Qué quieres promocionar?
                  </label>
                  <Textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="Ej: Suplemento natural para dolor de cabeza, funciona en 15 minutos..."
                    className="bg-white/80 border-gray-300 text-gray-800 placeholder-gray-500 min-h-[120px] resize-none text-lg focus:ring-2 focus:ring-[#3018ef] focus:border-transparent"
                    maxLength={500}
                  />
                  <div className="text-right text-sm text-gray-500 mt-2">
                    {prompt.length}/500
                  </div>
                </div>

                {/* Image Upload */}
                <div>
                  <label className="text-gray-800 font-semibold mb-3 block text-lg">
                    O sube imágenes de tu producto (opcional)
                  </label>
                  <div className="flex items-center gap-4">
                    <Button
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      className="border-[#3018ef] text-[#3018ef] hover:bg-[#3018ef] hover:text-white transition-all duration-300 px-6 py-3"
                    >
                      <Upload className="w-5 h-5 mr-2" />
                      Subir Imágenes
                    </Button>
                    <span className="text-gray-600 text-sm">
                      Máximo 3 imágenes
                    </span>
                  </div>
                  
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleImageUpload(e.target.files)}
                    className="hidden"
                  />

                  {/* Preview Images */}
                  {uploadedImages.length > 0 && (
                    <div className="flex gap-3 mt-4">
                      {uploadedImages.map((image, index) => (
                        <div key={index} className="relative">
                          <img
                            src={URL.createObjectURL(image)}
                            alt={`Preview ${index + 1}`}
                            className="w-24 h-24 object-cover rounded-xl border-2 border-gray-200 shadow-md"
                          />
                          <button
                            onClick={() => removeImage(index)}
                            className="absolute -top-2 -right-2 bg-[#dd3a5a] text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-[#c73650] transition-colors shadow-lg"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>



          {/* Generate Button */}
          <div className="text-center">
            <Button
              onClick={handleFreeGeneration}
              disabled={isGenerating || (!prompt.trim() && uploadedImages.length === 0)}
              className="bg-gradient-to-r from-[#dd3a5a] to-[#3018ef] hover:from-[#c73650] hover:to-[#2516d6] text-white px-16 py-6 text-xl font-bold rounded-full shadow-2xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {isGenerating ? (
                <>
                  <Sparkles className="w-7 h-7 mr-4 animate-spin" />
                  Emma está creando tu anuncio...
                </>
              ) : (
                <>
                  <Wand2 className="w-7 h-7 mr-4" />
                  Generar Nivel Premium
                  <Zap className="w-7 h-7 ml-4" />
                </>
              )}
            </Button>

            {!isGenerating && (
              <p className="text-gray-600 text-base mt-4 max-w-md mx-auto">
                Tecnología enterprise que genera campañas de calidad profesional instantáneamente
              </p>
            )}
          </div>

          {/* Generated Result */}
          {generatedResult && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-8"
            >
              <Card className="bg-white/70 backdrop-blur-sm border border-gray-200/50 shadow-xl">
                <CardContent className="p-8">
                  <div className="text-center mb-6">
                    <h3 className="text-3xl font-bold text-gray-800 mb-3">
                      🚀 ¡Campaña Premium Lista!
                    </h3>
                    <p className="text-gray-600 text-lg">
                      Generado con tecnología Emma Enterprise - Calidad nivel agencias
                    </p>
                  </div>

                  {/* Mostrar todas las versiones generadas */}
                  {generatedResult.all_images && generatedResult.all_images.length > 0 && (
                    <div className="mb-6">
                      <h4 className="text-lg font-semibold text-gray-800 mb-4 text-center">
                        🎨 {generatedResult.num_generated || generatedResult.all_images.length} Versiones Premium Generadas
                      </h4>
                      <div className={`grid gap-4 ${
                        generatedResult.all_images.length === 1 ? 'grid-cols-1 justify-center' :
                        generatedResult.all_images.length === 2 ? 'grid-cols-1 md:grid-cols-2' :
                        'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
                      }`}>
                        {generatedResult.all_images.map((imageUrl: string, index: number) => (
                          <div key={index} className="relative group">
                            <img
                              src={imageUrl}
                              alt={`Versión ${index + 1}`}
                              className="w-full rounded-xl shadow-lg border-2 border-gray-200 hover:shadow-2xl transition-all duration-300 cursor-pointer"
                              onClick={() => {
                                console.log(`🖱️ Clicked on image ${index + 1}:`, imageUrl);
                                handleImageClick(imageUrl);
                              }}
                            />
                            <div className="absolute top-2 left-2 bg-[#3018ef] text-white px-2 py-1 rounded-full text-xs font-semibold">
                              V{index + 1}
                            </div>
                            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                              <Button
                                size="sm"
                                onClick={(e) => {
                                  console.log(`📥 Hover download clicked for image ${index + 1}:`, imageUrl);
                                  e.stopPropagation();
                                  downloadImage(imageUrl, `emma-ad-v${index + 1}`);
                                }}
                                className="bg-[#dd3a5a] hover:bg-[#c73650] text-white p-2 rounded-full shadow-lg"
                              >
                                <Download className="w-4 h-4" />
                              </Button>
                            </div>
                            <div className="absolute inset-0 bg-black/0 hover:bg-black/10 rounded-xl transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                              <div className="bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-gray-800 flex items-center gap-2">
                                <ZoomIn className="w-4 h-4" />
                                Click para ampliar
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="text-center space-y-6">
                    <div className="text-sm text-gray-600 bg-gray-50 rounded-lg p-4">
                      <p className="mb-2"><strong>Plataforma:</strong> {generatedResult.platform}</p>
                      <p className="mb-2"><strong>Tamaño:</strong> {generatedResult.size}</p>
                      <p><strong>Versiones generadas:</strong> {generatedResult.num_generated || 1}</p>
                    </div>

                    <div className="flex gap-4 justify-center flex-wrap">
                      <Button
                        onClick={() => {
                          if (generatedResult.image_url) {
                            handleImageClick(generatedResult.image_url);
                          }
                        }}
                        className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:opacity-90 text-white px-6 py-3"
                      >
                        <ZoomIn className="w-5 h-5 mr-2" />
                        Ampliar Principal
                      </Button>

                      <Button
                        onClick={() => {
                          if (generatedResult.image_url) {
                            downloadImage(generatedResult.image_url, 'emma-ad-principal');
                          }
                        }}
                        variant="outline"
                        className="border-[#3018ef] text-[#3018ef] hover:bg-[#3018ef] hover:text-white px-6 py-3"
                      >
                        <Download className="w-5 h-5 mr-2" />
                        Descargar Principal
                      </Button>

                      {generatedResult.all_images && generatedResult.all_images.length > 1 && (
                        <Button
                          onClick={() => {
                            // Descargar todas las imágenes
                            generatedResult.all_images.forEach((url: string, index: number) => {
                              setTimeout(() => downloadImage(url, `emma-ad-v${index + 1}`), index * 500);
                            });
                          }}
                          variant="outline"
                          className="border-[#dd3a5a] text-[#dd3a5a] hover:bg-[#dd3a5a] hover:text-white px-6 py-3"
                        >
                          <Download className="w-5 h-5 mr-2" />
                          Descargar Todas
                        </Button>
                      )}

                      <Button
                        onClick={() => {
                          setGeneratedResult(null);
                          setPrompt('');
                          setUploadedImages([]);
                        }}
                        variant="outline"
                        className="border-gray-400 text-gray-600 hover:bg-gray-100 hover:text-gray-800 px-6 py-3"
                      >
                        <Sparkles className="w-5 h-5 mr-2" />
                        Nueva Campaña
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Features Premium */}
          {!generatedResult && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-16">
              {[
                { icon: ImageIcon, title: "Enterprise Visual", desc: "Calidad nivel agencias", color: "text-[#dd3a5a]" },
                { icon: Type, title: "Copy Premium", desc: "Headlines que convierten", color: "text-[#3018ef]" },
                { icon: Palette, title: "Brand Design", desc: "Diseño profesional", color: "text-purple-600" },
                { icon: Target, title: "High Performance", desc: "ROI maximizado", color: "text-green-600" }
              ].map((feature, index) => (
                <div key={index} className="text-center p-4 bg-white/50 backdrop-blur-sm rounded-xl border border-gray-200/50 hover:shadow-lg transition-all duration-300">
                  <feature.icon className={`w-10 h-10 ${feature.color} mx-auto mb-3`} />
                  <h4 className="text-gray-800 font-semibold text-base mb-1">{feature.title}</h4>
                  <p className="text-gray-600 text-sm">{feature.desc}</p>
                </div>
              ))}
            </div>
          )}
        </motion.div>
      </div>

      {/* Modal para ampliar imágenes */}
      {isModalOpen && (
        console.log('🔍 Rendering modal with:', { isModalOpen, selectedImage }) ||
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm"
          onClick={(e) => {
            // Cerrar modal si se hace clic en el fondo
            if (e.target === e.currentTarget) {
              setIsModalOpen(false);
            }
          }}
        >
          <div className="relative bg-white rounded-2xl shadow-2xl max-w-5xl w-full mx-4 max-h-[95vh] overflow-hidden">
            {/* Header del modal */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-white">
              <h3 className="text-2xl font-bold text-gray-800">Vista Ampliada - Emma Studio</h3>
              <div className="flex gap-3">
                <Button
                  onClick={() => {
                    console.log('📥 Download button clicked in modal');
                    selectedImage && downloadImage(selectedImage, 'emma-ad-ampliada');
                  }}
                  className="bg-[#dd3a5a] hover:bg-[#c73650] text-white px-4 py-2 shadow-lg"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Descargar
                </Button>
                <Button
                  onClick={() => {
                    console.log('📋 Copy URL button clicked in modal');
                    selectedImage && copyImageUrl(selectedImage);
                  }}
                  variant="outline"
                  className="border-[#3018ef] text-[#3018ef] hover:bg-[#3018ef] hover:text-white px-4 py-2"
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Copiar URL
                </Button>
                <Button
                  onClick={() => {
                    console.log('❌ Closing modal');
                    setIsModalOpen(false);
                  }}
                  variant="outline"
                  className="px-4 py-2 border-gray-300 hover:bg-gray-100"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Contenido del modal */}
            <div className="p-6 max-h-[80vh] overflow-auto bg-gray-50">
              {selectedImage && (
                <div className="flex justify-center">
                  <img
                    src={selectedImage}
                    alt="Vista ampliada"
                    className="max-w-full max-h-[70vh] object-contain rounded-lg shadow-lg bg-white"
                    onLoad={() => console.log('🖼️ Modal image loaded successfully')}
                    onError={(e) => {
                      console.error('❌ Modal image failed to load:', selectedImage);
                      console.error('Error event:', e);
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Componente principal con DashboardLayout
export default function FreeGenerationPage() {
  return (
    <DashboardLayout pageTitle="Generación Libre">
      <FreeGenerationContent />
    </DashboardLayout>
  );
}
