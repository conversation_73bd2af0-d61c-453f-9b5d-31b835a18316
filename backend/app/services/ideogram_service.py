"""
Ideogram.ai Service - Specialized service for generating advertisements using Ideogram.ai
Perfect for text-heavy marketing materials with professional quality at low cost.
"""

import logging
import httpx
import os
from typing import Dict, Any
from fastapi import UploadFile
from app.core.config import settings

logger = logging.getLogger(__name__)


class IdeogramService:
    """Service for generating advertisements using Ideogram.ai - optimized for text and marketing."""
    
    def __init__(self):
        """Initialize the Ideogram service."""
        self.api_key = os.getenv("IDEOGRAM_API_KEY") or settings.IDEOGRAM_API_KEY
        self.base_url = "https://api.ideogram.ai/v1"
        
    async def generate_image(self, prompt: str, dimensions: Dict[str, int] = None) -> Dict[str, Any]:
        """
        Generate an image using Ideogram.ai - main method for post generation.

        Args:
            prompt: Description of the image to create
            dimensions: Dictionary with width and height (e.g., {"width": 1080, "height": 1080})

        Returns:
            Dict with success status, image data, and metadata
        """
        # Convert dimensions to size string
        if dimensions:
            width = dimensions.get("width", 1024)
            height = dimensions.get("height", 1024)
            size = f"{width}x{height}"
        else:
            size = "1024x1024"

        # Use the existing generate_ad method
        return await self.generate_ad(prompt, size)

    async def generate_ad(self, prompt: str, size: str = "1024x1024") -> Dict[str, Any]:
        """
        Generate an advertisement using Ideogram.ai.
        
        Args:
            prompt: Description of the advertisement to create
            size: Image size (1024x1024, 1024x1792, 1792x1024)
            
        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}
        
        try:
            # Use the prompt directly as Ideogram recommends - don't over-complicate
            enhanced_prompt = prompt

            # Map common sizes to Ideogram format
            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Prepare multipart form data for Ideogram 3.0 Quality API (excellent for text)
            files = {
                "prompt": (None, enhanced_prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),  # Use QUALITY for best text rendering
                "magic_prompt": (None, "ON"),  # Force magic prompt ON for better text enhancement
                "style_type": (None, "DESIGN"),  # Use DESIGN style for better text integration
                "negative_prompt": (None, "blurry text, illegible text, distorted text, unreadable text, poor typography, bad font, unclear letters, pixelated text, low quality text, amateur typography, unprofessional text, watermark, signature, low resolution, blurry, distorted, poor quality, amateur design, bad composition, unclear, fuzzy text, messy text"),
                "num_images": (None, "1")  # Single image for individual calls
            }

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🎨 Generating advertisement with Ideogram.ai: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/ideogram-v3/generate",
                    files=files,
                    headers=headers
                )
                
                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}
                
                result = response.json()

                # Ideogram 3.0 response format
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]
                    image_url = image_data.get("url")

                    if image_url:
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("prompt"),
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "enhanced_prompt": enhanced_prompt,
                                "seed": image_data.get("seed"),
                                "is_image_safe": image_data.get("is_image_safe", True),
                                "style_type": image_data.get("style_type", "GENERAL")
                            }
                        }
                    else:
                        logger.error(f"No image URL found in response: {image_data}")
                        return {"success": False, "error": "No image URL in response"}
                else:
                    logger.error(f"No data array in response: {result}")
                    return {"success": False, "error": "No image data in response"}
                    
        except Exception as e:
            logger.error(f"Error generating advertisement with Ideogram: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def generate_multiple_ads(self, prompt: str, num_images: int = 3, size: str = "1024x1024") -> Dict[str, Any]:
        """
        Generate multiple advertisements in a single API call using Ideogram.ai.
        Uses best practices: QUALITY rendering, AUTO magic prompt, DESIGN style.

        Args:
            prompt: Description of the advertisement to create
            num_images: Number of images to generate (1-4, default 3)
            size: Image size

        Returns:
            Dict with success status, image URLs list, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}

        # Limit to API maximum
        num_images = min(max(num_images, 1), 8)

        try:
            # Use the prompt directly as Ideogram recommends - don't over-complicate
            enhanced_prompt = prompt

            # Map common sizes to Ideogram format
            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Prepare multipart form data for multiple images with enhanced text quality settings
            files = {
                "prompt": (None, enhanced_prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),  # Use QUALITY for best text rendering
                "magic_prompt": (None, "AUTO"),  # Use AUTO for better magic prompt enhancement
                "style_type": (None, "DESIGN"),  # DESIGN style is optimal for text-heavy advertisements
                "negative_prompt": (None, "blurry text, illegible text, distorted text, unreadable text, poor typography, bad font, unclear letters, pixelated text, low quality text, amateur typography, unprofessional text, watermark, signature, low resolution, blurry, distorted, poor quality, amateur design, bad composition, unclear, fuzzy text, messy text, pixelated, low contrast text, hard to read text"),
                "num_images": (None, str(num_images))  # Generate multiple images
            }

            logger.info(f"🎨 Requesting {num_images} images from Ideogram API...")
            logger.info(f"📝 Prompt length: {len(enhanced_prompt)} characters")
            logger.info(f"🎯 Using DESIGN style with QUALITY rendering for optimal text")

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🎨 Generating {num_images} advertisements with Ideogram.ai: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/ideogram-v3/generate",
                    files=files,
                    headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}

                result = response.json()

                # Process multiple images from response
                if "data" in result and len(result["data"]) > 0:
                    images = []
                    logger.info(f"📊 Processing {len(result['data'])} images from Ideogram response")

                    for i, image_data in enumerate(result["data"]):
                        image_url = image_data.get("url")
                        if image_url:
                            logger.info(f"✅ Image {i+1}: {image_url[:50]}...")
                            images.append({
                                "image_url": image_url,
                                "revised_prompt": image_data.get("prompt"),
                                "metadata": {
                                    "model": "ideogram-3.0-quality",
                                    "size": size,
                                    "resolution": ideogram_size,
                                    "original_prompt": prompt,
                                    "enhanced_prompt": enhanced_prompt,
                                    "seed": image_data.get("seed"),
                                    "variation": i + 1,
                                    "is_image_safe": image_data.get("is_image_safe", True),
                                    "style_type": image_data.get("style_type", "DESIGN")
                                }
                            })
                        else:
                            logger.warning(f"⚠️ Image {i+1} has no URL: {image_data}")

                    if images:
                        # Extract URLs for compatibility
                        image_urls = [img["image_url"] for img in images]
                        logger.info(f"🎉 Successfully processed {len(images)} images out of {len(result['data'])} received")

                        return {
                            "success": True,
                            "image_url": image_urls[0],  # First image as primary
                            "all_images": image_urls,
                            "num_generated": len(images),
                            "revised_prompt": images[0]["revised_prompt"],
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "enhanced_prompt": enhanced_prompt,
                                "num_requested": num_images,
                                "num_generated": len(images),
                                "type": "premium_batch_generation",
                                "rendering_speed": "QUALITY",
                                "magic_prompt": "AUTO",
                                "style_type": "DESIGN",
                                "all_images_data": images
                            }
                        }
                    else:
                        logger.error("❌ No valid images found in response data")
                        return {"success": False, "error": "No valid images in response"}
                else:
                    logger.error(f"❌ Invalid response structure: {result}")
                    return {"success": False, "error": "No image data in response"}

        except Exception as e:
            logger.error(f"Error generating multiple advertisements with Ideogram: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def generate_with_reference(self, prompt: str, reference_image: UploadFile, size: str = "1024x1024") -> Dict[str, Any]:
        """
        Generate advertisement using a reference image with Ideogram.ai.
        
        Args:
            prompt: Description of the advertisement to create
            reference_image: Reference image to guide the generation
            size: Image size
            
        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}
        
        try:
            # Read reference image content
            image_content = await reference_image.read()
            
            # Enhanced prompt for reference-based generation
            enhanced_prompt = f"""Create a professional advertisement inspired by the reference image style with this concept: {prompt}

STYLE ADAPTATION:
- Adapt the visual style, composition, and aesthetic from the reference image
- Maintain the professional quality and commercial appeal
- Integrate text elements naturally as shown in reference
- Use similar lighting, color palette, and mood
- Keep the same level of sophistication and brand appeal

TEXT REQUIREMENTS:
- Ensure all text is clearly readable and professionally integrated
- Match the typography style and placement approach from reference
- Maintain proper text hierarchy and contrast

The result should feel cohesive with the reference while being unique and professional."""

            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Prepare multipart form data with reference image for Ideogram 3.0 API
            files = {
                "prompt": (None, enhanced_prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),
                "magic_prompt": (None, "AUTO"),
                "style_type": (None, "GENERAL"),
                "negative_prompt": (None, "photorealistic humans, realistic people, photography of people, realistic portraits, realistic faces, human photography, portrait photography, realistic human figures, photographic style people, realistic man, realistic woman, lifelike humans, real person photos, selfie, crowd photography, blurry, low quality, pixelated, amateur, unprofessional, poor lighting, bad composition, illegible text, low resolution"),
                "num_images": (None, "1"),
                "style_reference_images": ("reference.png", image_content, "image/png")
            }

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🖼️ Generating with reference using Ideogram.ai: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/ideogram-v3/generate",
                    files=files,
                    headers=headers
                )
                
                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram reference error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}
                
                result = response.json()
                
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]
                    image_url = image_data.get("url")

                    if image_url:
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("prompt"),
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "enhanced_prompt": enhanced_prompt,
                                "seed": image_data.get("seed"),
                                "type": "reference_based",
                                "reference_used": True,
                                "is_image_safe": image_data.get("is_image_safe", True),
                                "style_type": image_data.get("style_type", "GENERAL")
                            }
                        }
                    else:
                        return {"success": False, "error": "No image URL in reference response"}
                else:
                    return {"success": False, "error": "No image data in reference response"}
                    
        except Exception as e:
            logger.error(f"Error in reference generation with Ideogram: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def generate_similar_image_with_reference(self, prompt: str, reference_image_url: str, reference_metadata: Dict[str, Any], size: str = "1024x1024") -> Dict[str, Any]:
        """
        Generate an image similar to a reference using Ideogram's style_reference_images parameter.
        Uses /generate endpoint with style_reference_images for similar (not remix) generation.

        Args:
            prompt: Description of the image to create
            reference_image_url: URL of the reference image to use as style guide
            reference_metadata: Metadata from the reference image
            size: Image size

        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}

        try:
            # Try to get image from local storage first
            from app.services.image_storage_service import image_storage_service

            reference_image_content = image_storage_service.read_stored_image(reference_image_url)

            if reference_image_content:
                logger.info(f"📁 Using locally stored reference image")
            else:
                # Fallback: try to download directly (will likely fail due to expiration)
                logger.warning(f"⚠️ Reference image not found in local storage, trying direct download")
                import httpx
                async with httpx.AsyncClient() as client:
                    response = await client.get(reference_image_url)
                    if response.status_code != 200:
                        logger.error(f"Failed to download reference image: {response.status_code}")
                        return {"success": False, "error": "Failed to download reference image - URL may have expired"}
                    reference_image_content = response.content

            # Extract reference parameters
            reference_style_type = reference_metadata.get("style_type", "DESIGN")

            # Map common sizes to Ideogram format
            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Prepare multipart form data with style_reference_images (CORRECT WAY FOR SIMILAR)
            files = {
                "prompt": (None, prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),
                "magic_prompt": (None, "AUTO"),
                "style_type": (None, reference_style_type),
                "negative_prompt": (None, "photorealistic humans, realistic people, photography of people, realistic portraits, realistic faces, human photography, portrait photography, realistic human figures, photographic style people, realistic man, realistic woman, lifelike humans, real person photos, selfie, crowd photography, blurry text, illegible text, distorted text, unreadable text, poor typography, low quality, pixelated, amateur, unprofessional, poor lighting, bad composition, watermark, low resolution"),
                "num_images": (None, "1"),
                "style_reference_images": ("reference.png", reference_image_content, "image/png")  # Style reference for similar images
            }

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🎨 Generating SIMILAR image using style_reference_images: {prompt[:100]}...")
            logger.info(f"🖼️ Using /generate with style_reference_images (official documentation)")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/ideogram-v3/generate",  # Use /generate endpoint for similar
                    files=files,
                    headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram similar image error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}

                result = response.json()

                # Ideogram 3.0 response format
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]
                    image_url = image_data.get("url")

                    if image_url:
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("prompt"),
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "seed": image_data.get("seed"),
                                "type": "style_reference_based",
                                "reference_used": True,
                                "is_image_safe": image_data.get("is_image_safe", True),
                                "style_type": image_data.get("style_type", reference_style_type),
                                "similarity_method": "style_reference_images"
                            }
                        }
                    else:
                        return {"success": False, "error": "No image URL in similar image response"}
                else:
                    return {"success": False, "error": "No image data in similar image response"}

        except Exception as e:
            logger.error(f"Error generating similar image with style reference: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def generate_image_with_seed(self, prompt: str, seed: int, style_type: str = "DESIGN", size: str = "1024x1024") -> Dict[str, Any]:
        """
        Generate an image using a specific seed for consistency.
        This is a more reliable approach for generating similar images.

        Args:
            prompt: Description of the image to create
            seed: Seed value for consistent generation
            style_type: Style type (DESIGN, GENERAL, etc.)
            size: Image size

        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}

        try:
            # Map common sizes to Ideogram format
            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Prepare multipart form data with seed
            files = {
                "prompt": (None, prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),
                "magic_prompt": (None, "AUTO"),
                "style_type": (None, style_type),
                "seed": (None, str(seed)),  # Use specific seed for similarity
                "negative_prompt": (None, "photorealistic humans, realistic people, photography of people, realistic portraits, realistic faces, human photography, portrait photography, realistic human figures, photographic style people, realistic man, realistic woman, lifelike humans, real person photos, selfie, crowd photography, blurry text, illegible text, distorted text, unreadable text, poor typography, low quality, pixelated, amateur, unprofessional, poor lighting, bad composition, watermark, low resolution"),
                "num_images": (None, "1")
            }

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🌱 Generating image with seed {seed}: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/ideogram-v3/generate",
                    files=files,
                    headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram seed-based error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}

                result = response.json()

                # Ideogram 3.0 response format
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]
                    image_url = image_data.get("url")

                    if image_url:
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("prompt"),
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "seed": image_data.get("seed", seed),
                                "style_type": image_data.get("style_type", style_type),
                                "is_similar_to_reference": True,
                                "similarity_method": "seed_based",
                                "is_image_safe": image_data.get("is_image_safe", True)
                            }
                        }
                    else:
                        logger.error(f"No image URL found in seed-based response: {image_data}")
                        return {"success": False, "error": "No image URL in response"}
                else:
                    logger.error(f"No data array in seed-based response: {result}")
                    return {"success": False, "error": "No image data in response"}

        except Exception as e:
            logger.error(f"Error generating image with seed: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}


# Global service instance
ideogram_service = IdeogramService()
